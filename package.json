{"name": "boilerplate-react-vite", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "yarn test --watch", "lint": "eslint src --ext ts,tsx", "lint:fix": "yarn lint --fix", "type-check": "tsc --project tsconfig.json --pretty --noEmit", "prepare": "husky install", "update-deps": "node ./src/config/update-deps.mjs"}, "dependencies": {"@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@types/jest": "29.0.3", "@types/react": "18.0.21", "@types/react-dom": "18.0.6", "@vitejs/plugin-react": "2.1.0", "babel-eslint": "10.1.0", "eslint": "8.24.0", "eslint-config-react-app": "7.0.1", "eslint-config-standard": "17.0.0", "eslint-config-standard-jsx": "11.0.0", "eslint-config-standard-react": "11.0.1", "eslint-plugin-import": "2.26.0", "eslint-plugin-n": "15.3.0", "eslint-plugin-promise": "6.0.1", "eslint-plugin-react": "7.31.8", "husky": "8.0.1", "jest": "29.1.1", "jest-runner-eslint": "1.1.0", "react": "18.2.0", "react-dom": "18.2.0", "react-test-renderer": "18.2.0", "ts-jest": "29.0.2", "ts-node": "10.9.1", "typescript": "4.8.4", "vite": "3.1.4", "vite-plugin-linter": "1.2.0", "vite-plugin-svgr": "2.2.1", "vite-tsconfig-paths": "3.5.1"}}